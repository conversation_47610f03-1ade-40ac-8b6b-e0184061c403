<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\Subscriber;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;
use Psr\SimpleCache\InvalidArgumentException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {}

    public function home()
    {
        return redirect()->to(url('/'), 301);
    }

    public function covid19(Request $request)
    {
        return redirect()->to('/', 301);
        //        $page = get_from_cache_by_slug('covid-19');
        //        incrementVisits($request, $page);
        //
        //        return view('frontend.statistics.covid20', compact('page'));
    }

    /**
     * Show the application dashboard.
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function index(Request $request)
    {
        $page = get_from_cache_by_slug('home');
        incrementVisits($request, $page);

        return $this->getView('index', compact('page'));
    }

    /**
     * @param  string  $viewName
     * @param  array  $data
     * @return Application|Factory|\Illuminate\Contracts\View\View
     */
    private function getView(string $viewName, array $data)
    {
        $themePath = $this->getThemePath();
        return view("{$themePath}.{$viewName}", $data);
    }

    /**
     * @return string
     */
    private function getThemePath(): string
    {
        $themeName = theme_service()->getCurrentThemeName() ?? get_theme_name();
        return "themes." . $themeName;
    }

    /**
     * Show the application blog index page .
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function blog(Request $request)
    {
        $page = get_from_cache_by_slug('blog');
        incrementVisits($request, $page);

        return $this->getView('blog.index', compact('page'));
    }

    /**
     * Show the application about us Page .
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function about_us(Request $request)
    {
        $page = get_from_cache_by_slug('about-us');
        incrementVisits($request, $page);

        return $this->getView('pages.about_us', compact('page'));
    }

    /**
     * Show the application contact us Page .
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function contact_us(Request $request): Renderable
    {
        $page = get_from_cache_by_slug('contact-us');

        incrementVisits($request, $page);

        return $this->getView('pages.contact_us', compact('page'));
    }

    /**
     * Show the application contact us Page .
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function skills(Request $request)
    {
        $page = get_from_cache_by_slug('skills');
        incrementVisits($request, $page);

        return $this->getView('pages.skills', compact('page'));
    }

    /**
     * Show the application contact us Page .
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function services(Request $request)
    {
        $page = get_from_cache_by_slug('services');
        incrementVisits($request, $page);

        return $this->getView('pages.services', compact('page'));
    }

    /**
     * Show the application contact us Page .
     *
     * @param  Request  $request
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function resume(Request $request)
    {
        $page = get_from_cache_by_slug('resume');
        incrementVisits($request, $page);

        return $this->getView('pages.resume', compact('page'));
    }

    /**
     * Show the application contact us Page .
     *
     * @param  Request  $request
     * @param         $slug
     *
     * @return Renderable
     * @throws InvalidArgumentException
     */
    public function page(Request $request, $slug)
    {
        $page = get_from_cache_by_slug($slug);
        incrementVisits($request, $page);

        return $this->getView("pages.{$slug}", compact('page'));
    }


    //    /**
    //     * download exported excel or pdf files and remove it
    //     * @param  Request  $request
    //     * @param $filePath
    //     * @return Application|RedirectResponse|Redirector|BinaryFileResponse
    //     */
    //    public function accessToFile(Request $request, $filePath)
    //    {
    ////        $file_is_avatar = (count(explode('/', $filePath)) and (explode('/', $filePath)[0] == 'users'));
    //
    //        // if you can access to file
    ////        if (auth()->check() or $file_is_avatar) {
    //            $pathToFile = storage_path("app/public/$filePath");
    //            if (Storage::disk('public')->exists($filePath) and !is_dir($pathToFile)) {
    //                return response()->file($pathToFile);
    //            }
    ////            abort(404);
    ////        }
    //
    ////        return redirect('login');
    //    }


    //    /**
    //     * Show the application contact us Page .
    //     *
    //     * @return void
    //     */
    //    public function site_map()
    //    {
    ////        $pages = Page::orderBy('updated_at', 'desc')->get();
    ////        return response()->view('front_end.sitemap.pages', [
    ////            'pages' => $pages,
    ////        ])->header('Content-Type', 'text/xml');
    //    }

    /**
     * @param  Request  $request
     * @return RedirectResponse
     */
    public function sendContactMessage(Request $request): RedirectResponse
    {
        $rules = [
            'name'    => 'required|string|max:255',
            'email'   => 'required|email|max:255',
            'subject' => 'required|string|max:250',
            'message' => 'required|string|max:550',
        ];

        $request->validate($rules);
        $contact = new  Message();
        $contact->name = $request->name;
        $contact->email = $request->email;
        $contact->message = $request->message;
        $contact->subject = $request->subject;
        $contact->save();

        //        ThanksMail
        //        NewContactMail
        return back()->with(['success' => "successfuly has been send your request $request->name"]);
        //            return \Redirect::to(\URL::previous().'#contact');
    }


    //    public function blue()
    //    {
    //        $user = User::first();
    //
    //        return view("frontend.themes.standard.old.blue", compact('user'));
    //    }
    //
    //    public function red()
    //    {
    //        $user = User::first();
    //
    //        return view("frontend.themes.standard.old.red", compact('user'));
    //    }

    /**
     * @param  Request  $request
     *
     * @return Factory|RedirectResponse|View
     * @throws ValidationException
     */
    public function storeSubscriber(Request $request)
    {
        $this->validate($request, [
            'email'  => ['required', Rule::unique('subscribers')->whereNull('deleted_at')],
            'locale' => 'required|max:10'
        ], [
            'email.unique' => 'Thank you. you have pre-registered to our mailing list.',
        ]);
        $subscribe = new Subscriber();
        $subscribe->email = $request->email;
        $subscribe->locale = $request->lang ?? 'en';
        $subscribe->save();
        $subscribe->addVisit($request);

        return back()->with(['success' => 'Thank you for subscription. we will send you a new service offers and news from our blog weekly.']);
    }

    /**
     * @param  Request  $request
     * @return BinaryFileResponse|void
     */
    public function downloadCv(Request $request)
    {
        $path = "cv/zahir-hayrullah-web-developer.pdf";
        $pathToFile = storage_path("data/$path");
        if (File::exists($pathToFile) and !is_dir($pathToFile)) {
            return response()->download($pathToFile, 'Zahir-Hayrullah-web-developer.pdf');
        }
        abort(404);
    }
}
