<?php

use App\Models\Page;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

if (!function_exists('lang')) {
    /**
     * @return string
     */
    function lang()
    {
        return app()->getLocale();
    }
}
/*--------------------------{</>}-------------------------*/
if (!function_exists('direct')) {
    /**
     * @return string
     */
    function direct()
    {
        return is_rtl() ? 'rtl' : 'ltr';
    }
}
/*--------------------------{</>}-------------------------*/
if (!function_exists('indexFollow')) {
    /**
     * @return string
     */
    function indexFollow()
    {
        return get_follow_index();
    }
}
/*--------------------------{</>}-------------------------*/

if (!function_exists('get_follow_index')) {
    function get_follow_index($endpoint = null)
    {
        $endpoint = $endpoint ?? config('app.url', 'https://zaherr.com');
        if (get_domain_name($endpoint) == "zaherr.com")
            return "index, follow";
        return "noindex, nofollow";
    }
}
if (!function_exists('get_domain_name')) {

    function get_domain_name($url)
    {
        $pieces = parse_url($url);
        $domain = isset($pieces['host']) ? $pieces['host'] : '';
        if (preg_match('/(?P<domain>[a-z0-9][a-z0-9\-]{1,63}\.[a-z\.]{2,6})$/i', $domain, $regs)) {
            return $regs['domain'];
        }
        return false;
    }
}
if (!function_exists('get_setting')) {

    /**
     * @param  string  $setting_name
     *
     * @return mixed
     */
    function get_setting($setting_name = 'site_name')
    {
        $key = 'setting_' . $setting_name . '_' . lang();
        if (!Cache::has($key)) {
            // 1-cache again
            Cache::rememberForever($key, function () use ($setting_name) {
                return optional(Setting::where('slug', $setting_name)->first())->value;
            });
        }

        return Cache::get($key);
    }
}
/*--------------------------{</>}-------------------------*/
if (!function_exists('CorporationSchema')) {

    /**
     * @return string
     */
    function CorporationSchema()
    {
        $site_name = config('app.name');
        $site_url = 'https://zaherr.com';
        $logo = "https://www.zaherr.com/assets/img/logo.png";
        $telephone = "+90-************";
        $email = "<EMAIL>";
        //        $search_url = "{$site_url}/search?key={key}";
        $street_address = "Başakşehir /Istanbul / Turkey";
        $facebook = get_setting('facebook');
        $instagram = get_setting('instagram');
        $twitter = get_setting('twitter');
        $sourcerer = get_setting('sourcerer');
        $github = get_setting('github');
        $linkedin = get_setting('linkedin');
        $scheme = '{"@context":"http://schema.org", "@type":"Organization", "name":"' . $site_name . '","url":"' . $site_url . '","logo": "' . $logo . '",'
            . '"contactPoint":[{ "@type": "ContactPoint", "telephone": "' . $telephone . '", "contactType": "Get in touch","email":"' . $email . '"}],'
            //            .'"potentialAction": {"@type":"SearchAction", "target":"'.$search_url.'", "query-input": "required name=key" },'
            . '"address": { "@type": "PostalAddress","streetAddress": "' . $street_address . '", "addressLocality": "Istanbul","postalCode": "34480","addressCountry": "Turkey"},'
            . '"sameAs":["' . $facebook . '", "' . $instagram . ' ", "' . $twitter . '","' . $linkedin . '", "' . $github . '", "' . $sourcerer . '"]}';

        return '<script data-schema="Organization" type="application/ld+json">' . $scheme . '</script>';
    }
}
/*--------------------------{</>}-------------------------*/

if (!function_exists('forget_cache')) {
    /**
     * @param $key
     *
     * @throws Exception
     */
    function forget_cache($key)
    {
        // delete this query from cache
        cache()->forget($key);
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('get_cache')) {
    /**
     * @param $key
     *
     * @return mixed
     * @throws Exception
     */
    function get_cache($key)
    {
        return cache()->get($key);
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('get_from_cache_by_slug')) {
    /**
     * @param        $slug
     * @param  string  $model
     *
     * @return mixed
     */
    function get_from_cache_by_slug($slug, string $model = Page::class)
    {

        //        return $model::active()->where('slug', $slug)->firstOrFail();
        $key = get_cache_key($slug, $model);
        // caching project if not existing
        if (!Cache::has($key)) {
            Cache::rememberForever($key, function () use ($model, $slug) {
                return $model::active()->where('slug', $slug)->firstOrFail();
            });
        }

        // get page from cache
        return Cache::get($key);
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('get_cache_key')) {
    /**
     * @param $slug
     * @param  string  $className
     *
     * @return mixed
     */
    function get_cache_key($slug, string $className = Page::class)
    {
        $name = get_model_name_by_class($className);

        return "{$name}_{$slug}"; // page_slug
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('get_model_name_by_class')) {
    /**
     * @param        $className
     * @param  string  $namespace
     *
     * @return mixed
     */
    function get_model_name_by_class($className = null, $namespace = 'App\\')
    {
        $name = str_replace($namespace, '', $className);
        $name = Str::lower($name);

        return $name;
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('get_site_info')) {
    /**
     * @return mixed
     */
    function get_site_info()
    {
        return [
            'name'      => config('app.name'),
            'url'       => 'https:/zaherr.com',
            'email'     => '<EMAIL>',
            'phone'     => '+90-************',
            'facebook'  => "https://facebook.com/zaherkhirullah",
            'instagram' => "https://instagram.com/zaherkhirullah",
            'twitter'   => "https://twitter.com/zaherkhirullah",
            'sourcerer' => "https://sourcerer.io/zaherkhirullah",
            'github'    => "https://github.com/zaherkhirullah",
            'linkedin'  => "https://linkedin.com/in/zaherkhirullah",

        ];
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('phoneLink')) {
    /**
     * @param  string  $title
     *
     * @return mixed
     */
    function phoneLink($title = '(+90)-************')
    {
        $url = "tel:" . get_setting('phone1');

        return "<a href='$url' target='_blank'>$title</a>";
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('whatsLink')) {
    /**
     * @param  string  $title
     *
     * @return mixed
     */
    function whatsLink($title = '(+90)-************')
    {
        $url = get_setting('whatsapp');

        return "<a href='$url' target='_blank'>$title</a>";
    }
}
/*--------------------------------------{</>}----------------------------------------*/

if (!function_exists('socialLink')) {
    /**
     * @param        $name
     *
     * @param  string  $title
     *
     * @return mixed
     */
    function socialLink($name, $title = '')
    {
        $url = get_setting($name) ?? "#";
        $title = $title ?? ucfirst($name);
        $title = $title ? "<span>$title</span>" : "<span hidden>$title</span>";

        return "<a href='$url' target='_blank' rel='nofollow'><i class='fab fa-$name'></i>$title</a>";
    }
}
/*--------------------------------------{</>}----------------------------------------*/
if (!function_exists('incrementVisits')) {
    /**
     * increment visits.
     *
     * @param        $request
     * @param        $row
     * @param  string  $key  is $key_visits_slug
     */
    function incrementVisits($request, $row, $key = 'page')
    {
        $key .= '_visits_' . $row->slug;
        if (!Session::has($key)) {
            $row->timestamps = false;
            $row->increment('visits');
            Session::put($key, 1);
            $row->addVisit($request);
        }
    }
}

/*--------------------------------------{</>}----------------------------------------*/
if (!function_exists('username')) {
    /**
     * increment visits.
     *
     * @return mixed
     */
    function username()
    {
        return auth()->check() ? auth()->user()->name : get_setting('site_name');
    }
}

/*--------------------------------------{</>}----------------------------------------*/
if (!function_exists('role_title')) {
    /**
     * increment visits.
     *
     * @return mixed
     */
    function role_title()
    {
        return optional(auth()->user()->role)->title;
    }
}

/************************************************
 * Views Functions
 *************************************************/
if (!function_exists('breadcrumbHome')) {

    function breadcrumbHome()
    {
        return "<li class='breadcrumb-item'><a href='" . url('/admin') . "'><i class='fas fa-dashboard'></i>" . __('Home') . "</a></li>";
    }
}
if (!function_exists('Roles')) {
    /**
     * @param  int  $count
     * @param  int  $withoutId
     * @param  bool  $random
     *
     * @return mixed
     */
    function Roles($count = null, $withoutId = null, $random = false)
    {
        $key = 'roles_' . lang();
        if (!Cache::has($key)) {
            Cache::rememberForever($key, function () use ($count, $withoutId, $random) {
                return \App\Models\Role::where(function ($q) use ($count, $withoutId, $random) {
                    if ($withoutId) {
                        $q->where('id', '!=', $withoutId);
                    }
                    if ($random != false) {
                        $q->inRandomOrder();
                    }
                    if ($count) {
                        $q->take($count);
                    }
                })->get();
            });
        }

        return Cache::get($key);
    }
}
/*------------------------=-{</>}-------------------------*/
if (!function_exists('Users')) {
    /**
     * @param  int  $count
     * @param  int  $withoutId
     * @param  bool  $random
     *
     * @return mixed
     */
    function Users($count = null, $withoutId = null, $random = false)
    {
        $key = 'users_' . lang();
        if (!Cache::has($key)) {
            Cache::rememberForever($key, function () use ($count, $withoutId, $random) {
                return \App\Models\User::where(function ($q) use ($count, $withoutId, $random) {
                    if ($withoutId) {
                        $q->where('id', '!=', $withoutId);
                    }
                    if ($random != false) {
                        $q->inRandomOrder();
                    }
                    if ($count) {
                        $q->take($count);
                    }
                })->get();
            });
        }

        return Cache::get($key);
    }
}
/*------------------------=-{</>}-------------------------*/
/*------------------------=-{</>}-------------------------*/
if (!function_exists('languages')) {

    function languages()
    {
        $langArr = array();
        foreach (langSymbols() as $key => $symbol) {
            $title = langTitles()[$key];
            $slug = langSlugs()[$key];
            $langArr[] = ['symbol' => $symbol, 'slug' => $slug, 'title' => $title];
        }

        return $langArr;
    }
}
/*------------------------=-{</>}-------------------------*/
if (!function_exists('is_en')) {

    function is_en($lang)
    {
        if (is_array($lang)) {
            return $lang['symbol'] == 'en' ? true : false;
        } else {
            return $lang == 'en' ? true : false;
        }
    }
}

/*------------------------=-{</>}-------------------------*/
if (!function_exists('langSymbols')) {

    function langSymbols()
    {
        return [
            'en',
            'tr',
            'ar',
        ];
    }
}
/*------------------------=-{</>}-------------------------*/
if (!function_exists('langSlugs')) {

    function langSlugs()
    {
        return [
            'english',
            'turkish',
            'arabic',
        ];
    }
}
/*------------------------=-{</>}-------------------------*/
if (!function_exists('langTitles')) {

    function langTitles()
    {
        return [
            'English',
            'Turkish',
            'Arabic',
        ];
    }
}
/*------------------------=-{</>}-------------------------*/

if (!function_exists('userAvatar')) {
    function userAvatar($name = null, $back = null, $color = null, $rounded = true)
    {
        if (!$name) {
            $name = username();
        }
        if (!$back) {
            $back = 'fff';
        }
        if (!$color) {
            $color = '007bff';
        }

        return "https://ui-avatars.com/api/?name={$name}&background={$back}&color={$color}&rounded={$rounded}";
    }
}

/*------------------------=-{</>}-------------------------*/
if (!function_exists('get_skills')) {
    function get_skills()
    {
        return [
            'php'                => 'PHP',
            'csharp'             => 'C#',
            'laravel'            => 'LARAVEL',
            'dotnet'             => '.NET',
            'postgresql'         => 'Postgresql',
            'mysql'              => 'MYSQL',
            'microsoftsqlserver' => 'SQL SERVER',
            'sqlite'             => 'Sqlite',
            'phpstorm'           => 'PhpStorm',
            'javascript'         => 'JAVASCRIPT',
            'vuedotjs'           => 'VUE JS',
            'jquery'             => 'JQUERY',
            'git'                => 'Git',
            'github'             => 'Github',
            'gitlab'             => 'Gitlab',
            'bitbucket'          => 'Bitbucket',
            'html5'              => 'HTML',
            'css3'               => 'CSS',
            //            'graphql'            => 'GraphQl',
        ];
    }
}
/*------------------------=-{</>}-------------------------*/

if (!function_exists('group_by')) {

    /**
     * @param $key
     * @param $data
     * @return array
     */
    function group_by($key, $data): array
    {
        $result = [];

        foreach ($data as $val) {
            if (array_key_exists($key, $val)) {
                $result[$val[$key]][] = $val;
            } else {
                $result[""][] = $val;
            }
        }

        return $result;
    }
}


if (!function_exists('theme_config')) {

    function theme_config($key, $default = null)
    {
        $theme = get_theme_name();
        return config("themes.$theme.$key", $default);
    }
}

if (!function_exists('get_theme_name')) {

    /**
     * @return string
     */
    function get_theme_name(): string
    {
        /***
         * Available themes:
         * standard
         * clark
         * kross
         * modern (new)
         **/
        return config('app.default_theme', 'modern');
    }
}

if (!function_exists('__trans')) {

    /**
     * @param $key
     * @return string
     */
    function __trans($key): string
    {
        return __($key);
    }
}


if (!function_exists('asset_theme_v')) {
    /**
     * @param $path
     * @param  string  $prefix
     * @return string
     */
    function asset_theme_v($path, string $prefix = 'v'): string
    {
        $themeName = get_theme_name();
        $path = "themes/{$themeName}/{$path}";
        return asset($path) . "?{$prefix}=" . version(public_path($path));
    }
}

/*------------------------=-{Modern Theme System}-------------------------*/

if (!function_exists('theme_service')) {
    /**
     * Get the theme service instance
     */
    function theme_service(): \App\Services\ThemeService
    {
        return app(\App\Services\ThemeService::class);
    }
}

if (!function_exists('theme_asset')) {
    /**
     * Generate theme asset URL
     */
    function theme_asset(string $path): string
    {
        return theme_service()->asset($path);
    }
}

if (!function_exists('theme_view')) {
    /**
     * Get theme view path
     */
    function theme_view(string $view): string
    {
        return theme_service()->getViewPath($view);
    }
}

if (!function_exists('theme_is')) {
    /**
     * Check if current theme matches
     */
    function theme_is(string $themeName): bool
    {
        return theme_service()->getCurrentThemeName() === $themeName;
    }
}

if (!function_exists('theme_supports')) {
    /**
     * Check if current theme supports a feature
     */
    function theme_supports(string $feature): bool
    {
        return theme_service()->supports($feature);
    }
}

if (!function_exists('theme_is_dark_mode')) {
    /**
     * Check if dark mode is enabled
     */
    function theme_is_dark_mode(): bool
    {
        return theme_service()->isDarkMode();
    }
}

if (!function_exists('theme_config_get')) {
    /**
     * Get theme configuration value
     */
    function theme_config_get(string $key = null, $default = null)
    {
        return theme_service()->getThemeConfig($key, $default);
    }
}

if (!function_exists('theme_switch')) {
    /**
     * Switch to a different theme
     */
    function theme_switch(string $themeName): bool
    {
        return theme_service()->switchTheme($themeName);
    }
}

if (!function_exists('theme_toggle_dark_mode')) {
    /**
     * Toggle dark mode
     */
    function theme_toggle_dark_mode(): bool
    {
        return theme_service()->toggleDarkMode();
    }
}

if (!function_exists('theme_get_available')) {
    /**
     * Get all available themes
     */
    function theme_get_available(): array
    {
        return theme_service()->getAvailableThemes();
    }
}


if (!function_exists('get_services')) {
    function get_services(): array
    {
        return [
            "Custom Web Application Development"           => "Building custom web applications tailored to the specific needs and requirements of the client.",
            "API Development"                              => "Creating robust APIs (Application Programming Interfaces) for seamless integration between different  systems and platforms.",
            "Database Design and Management"               => "Designing efficient database structures and managing data effectively using MySQL, PostgreSQL, or other database management systems.",
            "Content Management Systems (CMS) Development" => "Developing CMS solutions using Laravel to allow clients to manage their website content easily.",
            "Customer Relationship Management (CRM) Development" => "Developing CRM solutions with Laravel enables streamlined customer management, automated processes, and personalized interactions, enhancing efficiency and customer satisfaction.",
            "E-commerce Development"                       => "Building e-commerce platforms or adding e-commerce functionality to existing websites using Laravel and integrating payment gateways for secure transactions.",
            "Web Application Security"                     => "Implementing security measures to protect web applications from common threats such as SQL injection, cross-site scripting (XSS), and CSRF attacks.",
            "Performance Optimization"                     => "Optimizing web applications for performance and scalability to ensure fast loading times and smooth user experience.",
            "Maintenance and Support"                      => "Providing ongoing maintenance and support services to ensure the smooth operation of web applications and timely resolution of any issues that may arise.",
            "Integration Services"                         => "Integrating third-party services, APIs, and tools into web applications to enhance functionality and streamline business processes.",
            "Consultation and Technical Advice"            => "Offering consultation and technical advice to clients on web development best practices, technology choices, and project requirements.",
        ];
    }
}
