<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Modern Theme Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the modern theme.
    | You can customize various aspects of the theme here.
    |
    */

    'name' => 'modern',
    'title' => 'Zahir <PERSON>llah Theme',
    'description' => 'A modern, responsive portfolio theme built with Tailwind CSS and Alpine.js',
    'version' => '1.0.0',
    'author' => 'Your Name',

    /*
    |--------------------------------------------------------------------------
    | Theme Features
    |--------------------------------------------------------------------------
    */
    'features' => [
        'responsive' => true,
        'dark_mode' => true,
        'accessibility' => true,
        'lazy_loading' => true,
        'animations' => true,
        'seo_optimized' => true,
        'performance_optimized' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Hero Section Configuration
    |--------------------------------------------------------------------------
    */
    'hero' => [
        'name' => 'Zahir Hayrullah',
        'title' => 'Web Developer',
        'subtitle' => 'Backend Web Developer',
        'greeting' => 'Hello!',
        'location' => 'based in Istanbul',
        'description' => "I'm Zahir, backend web developer, I'm from Syria, I've been living in Turkey since 2013, I love my job, I see my job as a hobby for me. I have fun with codes and I think it's characteristic of coding. By coding we can make all our dreams come true.",
        'image' => 'images/hero-avatar.jpg',
        'background' => 'images/hero-bg.jpg',
        'show_social_links' => true,
        'show_scroll_indicator' => true,
        'animation_delay' => 500,
        'cta_primary' => [
            'text' => 'Hire me',
            'url' => '#contact-section',
        ],
        'cta_secondary' => [
            'text' => 'My works',
            'url' => '#services-section',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | About Section Configuration
    |--------------------------------------------------------------------------
    */
    'about' => [
        'name' => 'Zahir Hayrullah',
        'birthday' => 'January 01, 1996',
        'profession' => 'Web Developer',
        'title' => 'About Me',
        'subtitle' => 'Who is Zahir Hayrullah ?',
        'bio' => "I'm Zahir, <strong>backend web developer</strong>, I'm from Syria, I've been living in Turkey since 2013, I love my job, I see my job as a hobby for me,<br>I have fun with codes and I think it's characteristic of coding. By coding we can make all our dreams come true, I love learning new things and following all the new technical news especially when it comes to programming.<br><br>I like simple things, I just want to grab a cup of coffee and listen to some music and do some simple coding to solve complex problems in this world.",
        'description' => "I'm Zahir, backend web developer, I'm from Syria, I've been living in Turkey since 2013, I love my job, I see my job as a hobby for me. I have fun with codes and I think it's characteristic of coding. By coding we can make all our dreams come true.",
        'image' => 'images/about-image.jpg',
        'work_experience' => [
            'I worked for <a href="https://www.imtilakgroup.com" rel="nofollow" target="_blank">IMTILAK Group</a> is a group of twelve companies in many industries Real estate, Tourism, Medicine, Education, Architecture, Wholesale and E-Commerce.',
            'I\'m working now for <a href="https://contentfleet.com" rel="nofollow" target="_blank">Content Fleet</a>'
        ],
        'skills_highlight' => [
            'Backend Development',
            'API Development',
            'Database Design',
            'Web Applications',
        ],
        'years_experience' => \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')),
        'months_experience' => \Carbon\Carbon::now()->diffInMonths(\Carbon\Carbon::parse('2017-05-28')) - (\Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')) * 12),
        'projects_completed' => 26,
        'happy_clients' => 15,
        'footer' => "Zahir, a Syrian backend web developer based in Turkey since 2013. Passionate about coding, sees it as both a job and hobby. Believes in the power of coding to realize dreams. Enjoys learning and staying updated on tech, especially in programming.",
    ],

    /*
    |--------------------------------------------------------------------------
    | Services Section Configuration
    |--------------------------------------------------------------------------
    */
    'services' => [
        'title' => 'Services',
        'subtitle' => 'What I can do for you',
        'description' => 'Discover how I can help you! Explore a range of offerings tailored to meet your needs. From consulting to creative solutions, let\'s collaborate and achieve your goals together.',
        'items' => [
            [
                'icon' => 'code',
                'title' => 'Custom Web Application Development',
                'description' => 'Building custom web applications tailored to the specific needs and requirements of the client.',
                'features' => ['Laravel Framework', 'Custom Solutions', 'Scalable Architecture'],
            ],
            [
                'icon' => 'api',
                'title' => 'API Development',
                'description' => 'Creating robust APIs (Application Programming Interfaces) for seamless integration between different systems and platforms.',
                'features' => ['RESTful APIs', 'GraphQL', 'Third-party Integrations'],
            ],
            [
                'icon' => 'database',
                'title' => 'Database Design and Management',
                'description' => 'Designing efficient database structures and managing data effectively using MySQL, PostgreSQL, or other database management systems.',
                'features' => ['Database Design', 'Query Optimization', 'Data Migration'],
            ],
            [
                'icon' => 'cms',
                'title' => 'Content Management Systems (CMS) Development',
                'description' => 'Developing CMS solutions using Laravel to allow clients to manage their website content easily.',
                'features' => ['Custom CMS', 'User-friendly Interface', 'Content Management'],
            ],
            [
                'icon' => 'crm',
                'title' => 'CRM System Development',
                'description' => 'Develop comprehensive CRM systems to manage customer relationships and business processes.',
                'features' => ['Customer Management', 'Sales Pipeline', 'Reporting Dashboard'],
            ],
            [
                'icon' => 'management',
                'title' => 'Management Systems',
                'description' => 'Building specialized management systems for various industries and business needs.',
                'features' => ['School Management', 'Dental Management', 'Hotel Management'],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Skills Section Configuration
    |--------------------------------------------------------------------------
    */
    'skills' => [
        'title' => 'My Skills',
        'subtitle' => 'Technologies I work with',
        'description' => 'As a web developer, I excel in HTML, CSS, JavaScript, and PHP, creating dynamic and responsive websites with a keen eye for design and functionality.',
        'items' => [
            'php' => 'PHP',
            'csharp' => 'C#',
            'laravel' => 'LARAVEL',
            'dotnet' => '.NET',
            'postgresql' => 'Postgresql',
            'mysql' => 'MYSQL',
            'microsoftsqlserver' => 'SQL SERVER',
            'sqlite' => 'Sqlite',
            'phpstorm' => 'PhpStorm',
            'javascript' => 'JAVASCRIPT',
            'vuedotjs' => 'VUE JS',
            'jquery' => 'JQUERY',
            'git' => 'Git',
            'github' => 'Github',
            'gitlab' => 'Gitlab',
            'bitbucket' => 'Bitbucket',
            'html5' => 'HTML',
            'css3' => 'CSS',
        ],
        'show_experience_years' => true,
        'show_contact_info' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Portfolio Section Configuration
    |--------------------------------------------------------------------------
    */
    'portfolio' => [
        'title' => 'My Portfolio',
        'subtitle' => 'Recent projects I\'ve worked on',
        'categories' => [
            'all' => 'All Projects',
            'web' => 'Web Development',
            'mobile' => 'Mobile Apps',
            'design' => 'UI/UX Design',
        ],
        'items_per_page' => 6,
        'show_filters' => true,
        'show_modal' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Contact Section Configuration
    |--------------------------------------------------------------------------
    */
    'contact' => [
        'title' => 'Contact Me',
        'subtitle' => 'Get In Touch',
        'description' => 'Get in touch easily! Reach out for inquiries, collaborations, or just to say hello. Your messages are always welcome!',
        'email' => '<EMAIL>',
        'email_link' => 'mailto:<EMAIL>',
        'phone' => '+90 ************',
        'phone_link' => 'tel:+905511463411',
        'phone_whatsapp_link' => 'https://api.whatsapp.com/send?phone=905511463411',
        'address' => 'Berlin-Germany',
        'zipcode' => '34000',
        'website' => 'zaherr.com',
        'website_link' => 'https://zaherr.com',
        'show_contact_form' => true,
        'show_map' => false,
        'map_coordinates' => [
            'lat' => 41.0082,
            'lng' => 28.9784,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Color Scheme
    |--------------------------------------------------------------------------
    */
    'colors' => [
        'primary' => '#3b82f6',
        'secondary' => '#64748b',
        'accent' => '#f59e0b',
        'success' => '#10b981',
        'warning' => '#f59e0b',
        'error' => '#ef4444',
        'background' => '#ffffff',
        'surface' => '#f8fafc',
        'text' => '#1e293b',
    ],

    /*
    |--------------------------------------------------------------------------
    | Typography
    |--------------------------------------------------------------------------
    */
    'typography' => [
        'font_family' => 'Inter',
        'heading_font' => 'Inter',
        'body_font' => 'Inter',
        'font_sizes' => [
            'xs' => '0.75rem',
            'sm' => '0.875rem',
            'base' => '1rem',
            'lg' => '1.125rem',
            'xl' => '1.25rem',
            '2xl' => '1.5rem',
            '3xl' => '1.875rem',
            '4xl' => '2.25rem',
            '5xl' => '3rem',
            '6xl' => '3.75rem',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Layout Configuration
    |--------------------------------------------------------------------------
    */
    'layout' => [
        'container_width' => '1200px',
        'sidebar_width' => '300px',
        'header_height' => '80px',
        'footer_height' => '200px',
        'section_padding' => '80px',
        'grid_gap' => '2rem',
    ],

    /*
    |--------------------------------------------------------------------------
    | Animation Settings
    |--------------------------------------------------------------------------
    */
    'animations' => [
        'enabled' => true,
        'duration' => 800,
        'easing' => 'ease-in-out',
        'delay' => 100,
        'offset' => 100,
        'disable_on_mobile' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | SEO Configuration
    |--------------------------------------------------------------------------
    */
    'seo' => [
        'meta_title_template' => '%s | Zahir Hayrullah',
        'meta_description_default' => 'Zahir Hayrullah website showcasing web development skills and projects.',
        'meta_keywords_default' => 'web developer, portfolio, modern design, responsive',
        'og_image_default' => 'images/og-image.jpg',
        'twitter_card_type' => 'summary_large_image',
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'lazy_loading' => true,
        'image_optimization' => true,
        'css_minification' => true,
        'js_minification' => true,
        'gzip_compression' => true,
        'cache_duration' => 3600, // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Links
    |--------------------------------------------------------------------------
    */
    'social_links' => [
        'github' => 'https://github.com/zaherkhirullah',
        'linkedin' => 'https://linkedin.com/in/zaherkhirullah',
        'twitter' => 'https://twitter.com/zaherkhirullah',
        'instagram' => 'https://instagram.com/zaherkhirullah',
        'facebook' => 'https://facebook.com/zaherkhirullah',
        'gitlab' => '',
        'sourcerer' => 'https://sourcerer.io/zaherkhirullah',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom CSS/JS
    |--------------------------------------------------------------------------
    */
    'custom' => [
        'css' => '',
        'js' => '',
        'head_scripts' => '',
        'body_scripts' => '',
    ],
];
