<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Modern Theme Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the modern theme.
    | You can customize various aspects of the theme here.
    |
    */

    'name' => 'modern',
    'title' => 'Modern Portfolio Theme',
    'description' => 'A modern, responsive portfolio theme built with Tailwind CSS and Alpine.js',
    'version' => '1.0.0',
    'author' => 'Your Name',

    /*
    |--------------------------------------------------------------------------
    | Theme Features
    |--------------------------------------------------------------------------
    */
    'features' => [
        'responsive' => true,
        'dark_mode' => true,
        'accessibility' => true,
        'lazy_loading' => true,
        'animations' => true,
        'seo_optimized' => true,
        'performance_optimized' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Hero Section Configuration
    |--------------------------------------------------------------------------
    */
    'hero' => [
        'name' => env('HERO_NAME', 'John Doe'),
        'title' => env('HERO_TITLE', 'Full Stack Developer'),
        'description' => env('HERO_DESCRIPTION', 'I create beautiful, responsive websites and web applications using modern technologies. Let\'s build something amazing together.'),
        'image' => env('HERO_IMAGE', 'images/hero-avatar.jpg'),
        'background' => env('HERO_BACKGROUND', 'images/hero-bg.jpg'),
        'show_social_links' => true,
        'show_scroll_indicator' => true,
        'animation_delay' => 500,
    ],

    /*
    |--------------------------------------------------------------------------
    | About Section Configuration
    |--------------------------------------------------------------------------
    */
    'about' => [
        'title' => 'About Me',
        'subtitle' => 'Get to know me better',
        'description' => 'I am a passionate full-stack developer with expertise in modern web technologies.',
        'image' => 'images/about-image.jpg',
        'skills_highlight' => [
            'Frontend Development',
            'Backend Development',
            'Database Design',
            'API Development',
        ],
        'years_experience' => 5,
        'projects_completed' => 50,
        'happy_clients' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Services Section Configuration
    |--------------------------------------------------------------------------
    */
    'services' => [
        'title' => 'My Services',
        'subtitle' => 'What I can do for you',
        'items' => [
            [
                'icon' => 'code',
                'title' => 'Web Development',
                'description' => 'Custom web applications built with modern technologies',
                'features' => ['Responsive Design', 'Performance Optimization', 'SEO Friendly'],
            ],
            [
                'icon' => 'mobile',
                'title' => 'Mobile Development',
                'description' => 'Cross-platform mobile applications',
                'features' => ['iOS & Android', 'React Native', 'Flutter'],
            ],
            [
                'icon' => 'database',
                'title' => 'Backend Development',
                'description' => 'Robust server-side solutions and APIs',
                'features' => ['RESTful APIs', 'Database Design', 'Cloud Integration'],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Portfolio Section Configuration
    |--------------------------------------------------------------------------
    */
    'portfolio' => [
        'title' => 'My Portfolio',
        'subtitle' => 'Recent projects I\'ve worked on',
        'categories' => [
            'all' => 'All Projects',
            'web' => 'Web Development',
            'mobile' => 'Mobile Apps',
            'design' => 'UI/UX Design',
        ],
        'items_per_page' => 6,
        'show_filters' => true,
        'show_modal' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Contact Section Configuration
    |--------------------------------------------------------------------------
    */
    'contact' => [
        'title' => 'Get In Touch',
        'subtitle' => 'Let\'s work together',
        'description' => 'Have a project in mind? Let\'s discuss how we can bring your ideas to life.',
        'email' => env('CONTACT_EMAIL', '<EMAIL>'),
        'phone' => env('CONTACT_PHONE', '+****************'),
        'address' => env('CONTACT_ADDRESS', '123 Main St, City, Country'),
        'show_contact_form' => true,
        'show_map' => false,
        'map_coordinates' => [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Color Scheme
    |--------------------------------------------------------------------------
    */
    'colors' => [
        'primary' => '#3b82f6',
        'secondary' => '#64748b',
        'accent' => '#f59e0b',
        'success' => '#10b981',
        'warning' => '#f59e0b',
        'error' => '#ef4444',
        'background' => '#ffffff',
        'surface' => '#f8fafc',
        'text' => '#1e293b',
    ],

    /*
    |--------------------------------------------------------------------------
    | Typography
    |--------------------------------------------------------------------------
    */
    'typography' => [
        'font_family' => 'Inter',
        'heading_font' => 'Inter',
        'body_font' => 'Inter',
        'font_sizes' => [
            'xs' => '0.75rem',
            'sm' => '0.875rem',
            'base' => '1rem',
            'lg' => '1.125rem',
            'xl' => '1.25rem',
            '2xl' => '1.5rem',
            '3xl' => '1.875rem',
            '4xl' => '2.25rem',
            '5xl' => '3rem',
            '6xl' => '3.75rem',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Layout Configuration
    |--------------------------------------------------------------------------
    */
    'layout' => [
        'container_width' => '1200px',
        'sidebar_width' => '300px',
        'header_height' => '80px',
        'footer_height' => '200px',
        'section_padding' => '80px',
        'grid_gap' => '2rem',
    ],

    /*
    |--------------------------------------------------------------------------
    | Animation Settings
    |--------------------------------------------------------------------------
    */
    'animations' => [
        'enabled' => true,
        'duration' => 800,
        'easing' => 'ease-in-out',
        'delay' => 100,
        'offset' => 100,
        'disable_on_mobile' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | SEO Configuration
    |--------------------------------------------------------------------------
    */
    'seo' => [
        'meta_title_template' => '%s | Modern Portfolio',
        'meta_description_default' => 'Modern portfolio website showcasing web development skills and projects.',
        'meta_keywords_default' => 'web developer, portfolio, modern design, responsive',
        'og_image_default' => 'images/og-image.jpg',
        'twitter_card_type' => 'summary_large_image',
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'lazy_loading' => true,
        'image_optimization' => true,
        'css_minification' => true,
        'js_minification' => true,
        'gzip_compression' => true,
        'cache_duration' => 3600, // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Links
    |--------------------------------------------------------------------------
    */
    'social_links' => [
        'github' => env('SOCIAL_GITHUB'),
        'linkedin' => env('SOCIAL_LINKEDIN'),
        'twitter' => env('SOCIAL_TWITTER'),
        'instagram' => env('SOCIAL_INSTAGRAM'),
        'facebook' => env('SOCIAL_FACEBOOK'),
        'youtube' => env('SOCIAL_YOUTUBE'),
        'dribbble' => env('SOCIAL_DRIBBBLE'),
        'behance' => env('SOCIAL_BEHANCE'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom CSS/JS
    |--------------------------------------------------------------------------
    */
    'custom' => [
        'css' => '',
        'js' => '',
        'head_scripts' => '',
        'body_scripts' => '',
    ],
];
