<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Modern Theme Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the modern theme.
    | You can customize various aspects of the theme here.
    |
    */

    'name' => 'modern',
    'title' => 'Zahir <PERSON>llah Theme',
    'description' => 'A modern, responsive portfolio theme built with Tailwind CSS and Alpine.js',
    'version' => '1.0.0',
    'author' => 'Your Name',

    /*
    |--------------------------------------------------------------------------
    | Theme Features
    |--------------------------------------------------------------------------
    */
    'features' => [
        'responsive' => true,
        'dark_mode' => true,
        'accessibility' => true,
        'lazy_loading' => true,
        'animations' => true,
        'seo_optimized' => true,
        'performance_optimized' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Hero Section Configuration
    |--------------------------------------------------------------------------
    */
    'hero' => [
        'name' => 'Zahir Hayrullah',
        'title' => 'Web Developer',
        'subtitle' => 'Web Developer',
        'greeting' => 'Hello!',
        'location' => 'based in Berlin, Germany',
        'description' => "I'm Zahir, a passionate backend developer who transforms complex problems into elegant solutions. Originally from Syria, I've been crafting digital experiences in Berlin since 2022. For me, coding isn't just a profession—it's an art form where logic meets creativity.",
        'image' => 'images/hero-avatar.jpg',
        'background' => 'images/hero-bg.jpg',
        'show_social_links' => true,
        'show_scroll_indicator' => true,
        'animation_delay' => 500,
        'cta_primary' => [
            'text' => 'Hire me',
            'url' => '#contact-section',
        ],
        'cta_secondary' => [
            'text' => 'My works',
            'url' => '#services-section',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | About Section Configuration
    |--------------------------------------------------------------------------
    */
    'about' => [
        'name' => 'Zahir Hayrullah',
        'birthday' => 'January 01, 1996',
        'profession' => 'Web Developer',
        'title' => 'About Me',
        'subtitle' => 'Crafting Digital Solutions with Passion',
        'bio' => "I'm Zahir, a <strong>senior backend developer</strong> with over 7 years of experience building scalable web applications. Originally from Syria, I've been calling Berlin home since 2022, where I've grown from a curious developer into a seasoned architect of digital solutions.<br><br>My journey in tech is driven by an insatiable curiosity and the belief that elegant code can solve real-world problems. I specialize in creating robust APIs, designing efficient databases, and building systems that scale. Whether it's architecting a complex CRM system or optimizing database performance, I approach each challenge with creativity and precision.<br><br>When I'm not coding, you'll find me exploring the latest tech trends, contributing to open-source projects, or enjoying a perfect cup of coffee while sketching out solutions to tomorrow's challenges. For me, programming isn't just work—it's a continuous journey of learning, creating, and pushing the boundaries of what's possible.",
        'description' => "A passionate backend developer who transforms complex business requirements into elegant, scalable solutions. With expertise in modern web technologies and a keen eye for system architecture, I help businesses build robust digital foundations.",
        'image' => 'images/about-image.jpg',
        'work_experience' => [
            'Previously served as Senior Backend Developer at <a href="https://www.imtilakgroup.com" rel="nofollow" target="_blank">IMTILAK Group</a>, a diversified conglomerate spanning twelve companies across real estate, tourism, healthcare, education, architecture, wholesale, and e-commerce sectors. Led the development of scalable web applications and integrated systems across multiple business verticals.',
            'Currently working as Lead Backend Developer at <a href="https://contentfleet.com" rel="nofollow" target="_blank">Content Fleet</a>, where I architect and develop high-performance content management solutions, API integrations, and scalable backend systems that power modern digital experiences.'
        ],
        'skills_highlight' => [
            'Backend Development',
            'API Development',
            'Database Design',
            'Web Applications',
        ],
        'years_experience' => \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')),
        'months_experience' => \Carbon\Carbon::now()->diffInMonths(\Carbon\Carbon::parse('2017-05-28')) - (\Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')) * 12),
        'projects_completed' => 26,
        'happy_clients' => 15,
        'footer' => "Zahir Hayrullah - A Syrian-born backend developer thriving in Berlin since 2022. Passionate about transforming complex challenges into elegant digital solutions. Believes in the power of clean code to build the future, one application at a time.",
    ],

    /*
    |--------------------------------------------------------------------------
    | Services Section Configuration
    |--------------------------------------------------------------------------
    */
    'services' => [
        'title' => 'Services',
        'subtitle' => 'What I can do for you',
        'description' => 'I specialize in building robust, scalable backend solutions that power modern businesses. From enterprise-grade APIs to complex database architectures, I deliver technology solutions that drive growth and innovation.',
        'items' => [
            [
                'icon' => 'code',
                'title' => 'Enterprise Web Applications',
                'description' => 'Architecting and developing high-performance web applications that scale with your business. From concept to deployment, I create solutions that are secure, maintainable, and built for growth.',
                'features' => ['Laravel Ecosystem', 'Microservices Architecture', 'Cloud-Native Solutions'],
            ],
            [
                'icon' => 'api',
                'title' => 'API Architecture & Development',
                'description' => 'Designing and implementing robust API ecosystems that enable seamless integration and data flow between systems. Specializing in RESTful services and modern API patterns.',
                'features' => ['RESTful APIs', 'GraphQL Implementation', 'API Gateway Solutions'],
            ],
            [
                'icon' => 'database',
                'title' => 'Database Architecture & Optimization',
                'description' => 'Creating high-performance database solutions that handle complex queries and large datasets efficiently. From schema design to performance tuning, ensuring your data layer scales seamlessly.',
                'features' => ['Advanced Schema Design', 'Performance Optimization', 'Data Migration Strategies'],
            ],
            [
                'icon' => 'cms',
                'title' => 'Custom CMS Development',
                'description' => 'Building tailored content management systems that empower teams to manage digital content efficiently. User-friendly interfaces combined with powerful backend capabilities.',
                'features' => ['Headless CMS Solutions', 'Multi-tenant Architecture', 'Advanced Content Workflows'],
            ],
            [
                'icon' => 'crm',
                'title' => 'CRM & Business Systems',
                'description' => 'Developing comprehensive customer relationship management systems that streamline business processes and enhance customer engagement through intelligent automation.',
                'features' => ['Customer Journey Mapping', 'Automated Workflows', 'Advanced Analytics'],
            ],
            [
                'icon' => 'management',
                'title' => 'Industry-Specific Solutions',
                'description' => 'Creating specialized management systems tailored to specific industry requirements. From healthcare to education, delivering solutions that understand your unique challenges.',
                'features' => ['Healthcare Management', 'Educational Platforms', 'Hospitality Solutions'],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Skills Section Configuration
    |--------------------------------------------------------------------------
    */
    'skills' => [
        'title' => 'My Skills',
        'subtitle' => 'Technologies I work with',
        'description' => 'My technical expertise spans across modern backend technologies, database systems, and development tools. I continuously evolve my skill set to leverage cutting-edge technologies that deliver exceptional results.',
        'items' => [
            'php' => 'PHP',
            'csharp' => 'C#',
            'laravel' => 'LARAVEL',
            'dotnet' => '.NET',
            'postgresql' => 'Postgresql',
            'mysql' => 'MYSQL',
            'microsoftsqlserver' => 'SQL SERVER',
            'sqlite' => 'Sqlite',
            'phpstorm' => 'PhpStorm',
            'javascript' => 'JAVASCRIPT',
            'vuedotjs' => 'VUE JS',
            'jquery' => 'JQUERY',
            'git' => 'Git',
            'github' => 'Github',
            'gitlab' => 'Gitlab',
            'bitbucket' => 'Bitbucket',
            'html5' => 'HTML',
            'css3' => 'CSS',
        ],
        'show_experience_years' => true,
        'show_contact_info' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Portfolio Section Configuration
    |--------------------------------------------------------------------------
    */
    'portfolio' => [
        'title' => 'My Portfolio',
        'subtitle' => 'Recent projects I\'ve worked on',
        'categories' => [
            'all' => 'All Projects',
            'web' => 'Web Development',
            'mobile' => 'Mobile Apps',
            'design' => 'UI/UX Design',
        ],
        'items_per_page' => 6,
        'show_filters' => true,
        'show_modal' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Contact Section Configuration
    |--------------------------------------------------------------------------
    */
    'contact' => [
        'title' => 'Contact Me',
        'subtitle' => 'Get In Touch',
        'description' => 'Ready to bring your next project to life? Let\'s discuss how we can transform your ideas into powerful digital solutions. I\'m always excited to tackle new challenges and collaborate on innovative projects.',
        'email' => '<EMAIL>',
        'email_link' => 'mailto:<EMAIL>',
        'phone' => '+49 ************',
        'phone_link' => 'tel:+4915114634111',
        'phone_whatsapp_link' => 'https://api.whatsapp.com/send?phone=4915114634111',
        'address' => 'Berlin, Germany',
        'zipcode' => '10115',
        'website' => 'zaherr.com',
        'website_link' => 'https://zaherr.com',
        'show_contact_form' => true,
        'show_map' => false,
        'map_coordinates' => [
            'lat' => 52.5200,
            'lng' => 13.4050,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Color Scheme
    |--------------------------------------------------------------------------
    */
    'colors' => [
        'primary' => '#3b82f6',
        'secondary' => '#64748b',
        'accent' => '#f59e0b',
        'success' => '#10b981',
        'warning' => '#f59e0b',
        'error' => '#ef4444',
        'background' => '#ffffff',
        'surface' => '#f8fafc',
        'text' => '#1e293b',
    ],

    /*
    |--------------------------------------------------------------------------
    | Typography
    |--------------------------------------------------------------------------
    */
    'typography' => [
        'font_family' => 'Inter',
        'heading_font' => 'Inter',
        'body_font' => 'Inter',
        'font_sizes' => [
            'xs' => '0.75rem',
            'sm' => '0.875rem',
            'base' => '1rem',
            'lg' => '1.125rem',
            'xl' => '1.25rem',
            '2xl' => '1.5rem',
            '3xl' => '1.875rem',
            '4xl' => '2.25rem',
            '5xl' => '3rem',
            '6xl' => '3.75rem',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Layout Configuration
    |--------------------------------------------------------------------------
    */
    'layout' => [
        'container_width' => '1200px',
        'sidebar_width' => '300px',
        'header_height' => '80px',
        'footer_height' => '200px',
        'section_padding' => '80px',
        'grid_gap' => '2rem',
    ],

    /*
    |--------------------------------------------------------------------------
    | Animation Settings
    |--------------------------------------------------------------------------
    */
    'animations' => [
        'enabled' => true,
        'duration' => 800,
        'easing' => 'ease-in-out',
        'delay' => 100,
        'offset' => 100,
        'disable_on_mobile' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | SEO Configuration
    |--------------------------------------------------------------------------
    */
    'seo' => [
        'meta_title_template' => '%s | Zahir Hayrullah - Senior Backend Developer',
        'meta_description_default' => 'Zahir Hayrullah - Senior Backend Developer specializing in Laravel, API development, and scalable web applications. Based in Berlin, Germany.',
        'meta_keywords_default' => 'backend developer, Laravel expert, API development, web applications, Berlin developer, PHP developer',
        'og_image_default' => 'images/og-image.jpg',
        'twitter_card_type' => 'summary_large_image',
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'lazy_loading' => true,
        'image_optimization' => true,
        'css_minification' => true,
        'js_minification' => true,
        'gzip_compression' => true,
        'cache_duration' => 3600, // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Links
    |--------------------------------------------------------------------------
    */
    'social_links' => [
        'github' => 'https://github.com/zaherkhirullah',
        'linkedin' => 'https://linkedin.com/in/zaherkhirullah',
        'twitter' => 'https://twitter.com/zaherkhirullah',
        'instagram' => 'https://instagram.com/zaherkhirullah',
        'facebook' => 'https://facebook.com/zaherkhirullah',
        'gitlab' => '',
        'sourcerer' => 'https://sourcerer.io/zaherkhirullah',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom CSS/JS
    |--------------------------------------------------------------------------
    */
    'custom' => [
        'css' => '',
        'js' => '',
        'head_scripts' => '',
        'body_scripts' => '',
    ],
];
