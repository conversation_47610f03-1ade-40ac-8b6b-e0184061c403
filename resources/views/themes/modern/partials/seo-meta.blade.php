@php
    // Set default values for SEO variables
    $pageTitle = $pageTitle ?? (trim($__env->yieldContent('title')) ?: config('app.name', 'Portfolio'));
    $pageDescription =
        $pageDescription ??
        (trim($__env->yieldContent('description')) ?:
            'Modern portfolio website showcasing web development skills and projects.');
    $pageKeywords =
        $pageKeywords ??
        (trim($__env->yieldContent('keywords')) ?: 'web developer, portfolio, modern design, responsive, full-stack');
    $pageAuthor = $pageAuthor ?? (trim($__env->yieldContent('author')) ?: config('app.name'));
    $ogTitle = $ogTitle ?? (trim($__env->yieldContent('og_title')) ?: $pageTitle);
    $ogDescription = $ogDescription ?? (trim($__env->yieldContent('og_description')) ?: $pageDescription);
    $ogImage = $ogImage ?? (trim($__env->yieldContent('og_image')) ?: theme_asset('images/og-image.jpg'));
    $twitterTitle = $twitterTitle ?? (trim($__env->yieldContent('twitter_title')) ?: $pageTitle);
    $twitterDescription =
        $twitterDescription ?? (trim($__env->yieldContent('twitter_description')) ?: $pageDescription);
    $twitterImage = $twitterImage ?? (trim($__env->yieldContent('twitter_image')) ?: $ogImage);
    $canonicalUrl = $canonicalUrl ?? (trim($__env->yieldContent('canonical')) ?: url()->current());
@endphp

<!-- Primary Meta Tags -->
<title>{{ $pageTitle }}</title>
<meta name="title" content="{{ $pageTitle }}">
<meta name="description" content="{{ $pageDescription }}">
<meta name="keywords" content="{{ $pageKeywords }}">
<meta name="author" content="{{ $pageAuthor }}">
<meta name="robots" content="index, follow">
<meta name="language" content="{{ str_replace('_', '-', app()->getLocale()) }}">
<meta name="revisit-after" content="7 days">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ $canonicalUrl }}">
<meta property="og:title" content="{{ $ogTitle }}">
<meta property="og:description" content="{{ $ogDescription }}">
<meta property="og:image" content="{{ $ogImage }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:site_name" content="{{ config('app.name') }}">
<meta property="og:locale" content="{{ str_replace('_', '-', app()->getLocale()) }}">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ $canonicalUrl }}">
<meta property="twitter:title" content="{{ $twitterTitle }}">
<meta property="twitter:description" content="{{ $twitterDescription }}">
<meta property="twitter:image" content="{{ $twitterImage }}">
@if (theme_config_get('social_links.twitter'))
    <meta property="twitter:site" content="{{ '@' . basename(theme_config_get('social_links.twitter')) }}">
    <meta property="twitter:creator" content="{{ '@' . basename(theme_config_get('social_links.twitter')) }}">
@endif

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="{{ theme_config_get('colors.primary', '#3b82f6') }}">
<meta name="msapplication-TileColor" content="{{ theme_config_get('colors.primary', '#3b82f6') }}">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="{{ config('app.name') }}">

<!-- Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "{{ theme_config_get('hero.name', config('app.name')) }}",
    "jobTitle": "{{ theme_config_get('hero.title', 'Web Developer') }}",
    "description": "{{ $pageDescription }}",
    "url": "{{ url('/') }}",
    "image": "{{ theme_asset(theme_config_get('hero.image', 'images/hero-avatar.jpg')) }}",
    "sameAs": [
        @php
            $socialLinks = [];
            if(theme_config_get('social_links.github')) $socialLinks[] = theme_config_get('social_links.github');
            if(theme_config_get('social_links.linkedin')) $socialLinks[] = theme_config_get('social_links.linkedin');
            if(theme_config_get('social_links.twitter')) $socialLinks[] = theme_config_get('social_links.twitter');
        @endphp
        @foreach($socialLinks as $index => $link)
            "{{ $link }}"{{ $index < count($socialLinks) - 1 ? ',' : '' }}
        @endforeach
    ],
    "contactPoint": {
        "@type": "ContactPoint",
        "email": "{{ theme_config_get('contact.email') }}",
        "telephone": "{{ theme_config_get('contact.phone') }}",
        "contactType": "professional"
    },
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ theme_config_get('contact.address') }}"
    }
}
</script>

<!-- Website Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{{ config('app.name') }}",
    "description": "{{ $pageDescription }}",
    "url": "{{ url('/') }}",
    "author": {
        "@type": "Person",
        "name": "{{ theme_config_get('hero.name', config('app.name')) }}"
    },
    "inLanguage": "{{ str_replace('_', '-', app()->getLocale()) }}",
    "copyrightYear": "{{ date('Y') }}",
    "copyrightHolder": {
        "@type": "Person",
        "name": "{{ theme_config_get('hero.name', config('app.name')) }}"
    }
}
</script>

@if (request()->is('/'))
    <!-- Organization Structured Data for Homepage -->
    <script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": "{{ theme_config_get('hero.name', config('app.name')) }}",
    "description": "{{ theme_config_get('hero.description', $pageDescription) }}",
    "url": "{{ url('/') }}",
    "logo": "{{ theme_asset(theme_config_get('logo', 'images/logo.png')) }}",
    "image": "{{ theme_asset(theme_config_get('hero.image', 'images/hero-avatar.jpg')) }}",
    "telephone": "{{ theme_config_get('contact.phone') }}",
    "email": "{{ theme_config_get('contact.email') }}",
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ theme_config_get('contact.address') }}"
    },
    "serviceType": "Web Development",
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Web Development Services",
        "itemListElement": [
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Web Development",
                    "description": "Custom web applications and websites"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Mobile Development",
                    "description": "Cross-platform mobile applications"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Backend Development",
                    "description": "Server-side solutions and APIs"
                }
            }
        ]
    }
}
</script>
@endif
