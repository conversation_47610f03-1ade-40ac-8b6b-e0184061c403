<section id="skills" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">Skills & Expertise</h2>
                <p class="section-subtitle">
                    Technologies and tools I work with to create amazing digital experiences
                </p>
            </div>
            
            <!-- Skills Content -->
            <div class="grid md:grid-cols-2 gap-12">
                
                <!-- Technical Skills -->
                <div data-animate>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">Technical Skills</h3>
                    
                    <div class="space-y-6">
                        @php
                            $skills = [
                                ['name' => 'Laravel/PHP', 'level' => 95],
                                ['name' => 'JavaScript/Vue.js', 'level' => 90],
                                ['name' => 'HTML/CSS', 'level' => 95],
                                ['name' => 'MySQL/Database', 'level' => 85],
                                ['name' => 'Git/Version Control', 'level' => 90],
                                ['name' => 'Docker/DevOps', 'level' => 80],
                            ];
                        @endphp
                        
                        @foreach($skills as $skill)
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-700 dark:text-gray-300 font-medium">{{ $skill['name'] }}</span>
                                    <span class="text-primary-600 dark:text-primary-400 font-medium">{{ $skill['level'] }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-primary-600 to-purple-600 h-2 rounded-full transition-all duration-1000 ease-out" 
                                         style="width: {{ $skill['level'] }}%"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                
                <!-- Soft Skills -->
                <div data-animate>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">Professional Skills</h3>
                    
                    <div class="grid grid-cols-2 gap-6">
                        @php
                            $softSkills = [
                                ['name' => 'Problem Solving', 'icon' => 'puzzle'],
                                ['name' => 'Team Leadership', 'icon' => 'users'],
                                ['name' => 'Communication', 'icon' => 'chat'],
                                ['name' => 'Project Management', 'icon' => 'clipboard'],
                                ['name' => 'Creative Thinking', 'icon' => 'lightbulb'],
                                ['name' => 'Time Management', 'icon' => 'clock'],
                            ];
                        @endphp
                        
                        @foreach($softSkills as $skill)
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-300">
                                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-3">
                                    <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <h4 class="font-medium text-gray-900 dark:text-white text-sm">{{ $skill['name'] }}</h4>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
