<section id="about" class="section bg-gray-50 dark:bg-gray-800/50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">About Me</h2>
                <p class="section-subtitle">
                    Get to know me better and learn about my journey in web development
                </p>
            </div>
            
            <div class="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                
                <!-- Content -->
                <div class="space-y-6" data-animate>
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                            {{ theme_config_get('about.title', 'Passionate Developer') }}
                        </h3>
                        <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                            {{ theme_config_get('about.description', 'I am a passionate full-stack developer with expertise in modern web technologies. I love creating beautiful, functional, and user-friendly applications that solve real-world problems.') }}
                        </p>
                    </div>
                    
                    <!-- Skills Highlight -->
                    <div class="grid grid-cols-2 gap-4">
                        @foreach(theme_config_get('about.skills_highlight', ['Frontend Development', 'Backend Development', 'Database Design', 'API Development']) as $skill)
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300">{{ $skill }}</span>
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                {{ theme_config_get('about.years_experience', 5) }}+
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Years Experience</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                {{ theme_config_get('about.projects_completed', 50) }}+
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Projects Completed</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                {{ theme_config_get('about.happy_clients', 30) }}+
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Happy Clients</div>
                        </div>
                    </div>
                    
                    <!-- CTA Button -->
                    <div class="pt-6">
                        <a href="#contact" 
                           class="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <span>Let's Work Together</span>
                            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Image -->
                <div class="relative" data-animate>
                    <div class="relative">
                        <!-- Main Image -->
                        <div class="relative overflow-hidden rounded-2xl shadow-2xl">
                            <img src="{{ theme_config_get('about.image', theme_asset('images/about-image.jpg')) }}" 
                                 alt="About {{ theme_config_get('hero.name', 'Me') }}"
                                 class="w-full h-96 lg:h-[500px] object-cover">
                            
                            <!-- Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                        </div>
                        
                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -right-4 w-24 h-24 bg-primary-500/20 rounded-full blur-xl"></div>
                        <div class="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl"></div>
                        
                        <!-- Floating Card -->
                        <div class="absolute bottom-6 left-6 bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Available for work</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Technologies -->
            <div class="mt-20" data-animate>
                <div class="text-center mb-12">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Technologies I Work With</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Here are some of the technologies and tools I use to bring ideas to life
                    </p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                    @php
                        $technologies = [
                            ['name' => 'Laravel', 'icon' => 'laravel.svg'],
                            ['name' => 'Vue.js', 'icon' => 'vue.svg'],
                            ['name' => 'React', 'icon' => 'react.svg'],
                            ['name' => 'Tailwind CSS', 'icon' => 'tailwind.svg'],
                            ['name' => 'MySQL', 'icon' => 'mysql.svg'],
                            ['name' => 'Docker', 'icon' => 'docker.svg'],
                        ];
                    @endphp
                    
                    @foreach($technologies as $tech)
                        <div class="group">
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
                                <div class="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                                    <!-- You can replace this with actual tech icons -->
                                    <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-purple-500 rounded-lg flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">{{ substr($tech['name'], 0, 2) }}</span>
                                    </div>
                                </div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white text-center group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
                                    {{ $tech['name'] }}
                                </h4>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>
