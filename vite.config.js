import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/js/themes/modern/app.js',
                'resources/sass/themes/modern/app.scss'
            ],
            refresh: true,
        }),
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['alpinejs', 'axios'],
                    ui: ['@headlessui/js', 'swiper']
                }
            }
        }
    },
    server: {
        hmr: {
            host: 'localhost',
        },
    },
});
